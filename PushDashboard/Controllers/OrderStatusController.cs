using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.ViewModels;
using PushDashboard.Services.Integrations.Common.Models;
using PushDashboard.Services.Integrations;
using System.Security.Claims;
using PushDashboard.Services;

namespace PushDashboard.Controllers;

[Authorize]
public class OrderStatusController : BaseController
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<OrderStatusController> _logger;
    private readonly IEcommerceServiceFactory _ecommerceServiceFactory;
    private readonly IEmailTemplateService _emailTemplateService;
    private readonly IWhatsAppTemplateService _whatsAppTemplateService;

    public OrderStatusController(ApplicationDbContext context, ILogger<OrderStatusController> logger, IEcommerceServiceFactory ecommerceServiceFactory, IEmailTemplateService emailTemplateService, IWhatsAppTemplateService whatsAppTemplateService)
    {
        _context = context;
        _logger = logger;
        _ecommerceServiceFactory = ecommerceServiceFactory;
        _emailTemplateService = emailTemplateService;
        _whatsAppTemplateService = whatsAppTemplateService;
    }

    public async Task<IActionResult> Index()
    {
        var companyId = GetCompanyId();
        if (companyId == null)
        {
            return RedirectToAction("Index", "Home");
        }

        // Aktif iletişim kanallarını kontrol et
        var activeChannels = await GetActiveChannels(companyId.Value);

        var viewModel = new OrderStatusNotificationIndexViewModel
        {
            OrderStatuses = GetOrderStatuses(),
            Notifications = await GetOrderStatusNotifications(companyId.Value),
            EmailTemplates = activeChannels.HasEmail ? await GetEmailTemplates(companyId.Value) : new List<TemplateSelectItem>(),
            SmsTemplates = activeChannels.HasSms ? await GetSmsTemplates(companyId.Value) : new List<TemplateSelectItem>(),
            WhatsAppTemplates = activeChannels.HasWhatsApp ? await GetWhatsAppTemplates(companyId.Value) : new List<TemplateSelectItem>(),
            ActiveChannels = activeChannels
        };

        return View(viewModel);
    }

    [HttpPost]
    public async Task<IActionResult> SaveNotificationSettings([FromBody] SaveOrderStatusNotificationRequest request)
    {
        try
        {
            var companyId = GetCompanyId();
            if (companyId == null)
            {
                return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
            }

            // Mevcut ayarı bul veya yeni oluştur
            var notification = await _context.OrderStatusNotifications
                .FirstOrDefaultAsync(osn => osn.CompanyId == companyId.Value && osn.OrderStatus == request.OrderStatus);

            if (notification == null)
            {
                notification = new OrderStatusNotification
                {
                    CompanyId = companyId.Value,
                    OrderStatus = request.OrderStatus,
                    OrderStatusDisplayName = GetOrderStatusDisplayName(request.OrderStatus),
                    CreatedAt = DateTime.UtcNow
                };
                _context.OrderStatusNotifications.Add(notification);
            }

            // Ayarları güncelle
            notification.IsActive = request.IsActive;
            notification.EmailNotificationEnabled = request.EmailNotificationEnabled;
            notification.EmailTemplateId = request.EmailTemplateId;
            notification.SmsNotificationEnabled = request.SmsNotificationEnabled;
            notification.SmsTemplateId = request.SmsTemplateId;
            notification.WhatsAppNotificationEnabled = request.WhatsAppNotificationEnabled;
            notification.WhatsAppTemplateId = request.WhatsAppTemplateId;
            notification.DelayMinutes = request.DelayMinutes;
            notification.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Order status notification settings saved for company {CompanyId}, status {OrderStatus}",
                companyId.Value, request.OrderStatus);

            return Json(new { success = true, message = "Ayarlar başarıyla kaydedildi." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving order status notification settings");
            return Json(new { success = false, message = "Ayarlar kaydedilirken hata oluştu." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> ToggleNotification([FromBody] ToggleNotificationRequest request)
    {
        try
        {
            var companyId = GetCompanyId();
            if (companyId == null)
            {
                return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
            }

            var notification = await _context.OrderStatusNotifications
                .FirstOrDefaultAsync(osn => osn.CompanyId == companyId.Value && osn.OrderStatus == request.OrderStatus);

            if (notification == null)
            {
                return Json(new { success = false, message = "Bildirim ayarı bulunamadı." });
            }

            notification.IsActive = request.IsActive;
            notification.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            return Json(new { success = true, message = "Bildirim durumu güncellendi." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling notification status");
            return Json(new { success = false, message = "Bildirim durumu güncellenirken hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetOrderStatusLogs(int page = 1, int pageSize = 20, string? orderStatus = null)
    {
        try
        {
            var companyId = GetCompanyId();
            if (companyId == null)
            {
                return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
            }

            var query = _context.OrderStatusChangeLogs
                .Where(oscl => oscl.CompanyId == companyId.Value);

            if (!string.IsNullOrEmpty(orderStatus))
            {
                query = query.Where(oscl => oscl.NewStatus == orderStatus);
            }

            var totalCount = await query.CountAsync();
            var logs = await query
                .OrderByDescending(oscl => oscl.StatusChangedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(oscl => new OrderStatusChangeLogViewModel
                {
                    Id = oscl.Id,
                    OrderId = oscl.OrderId,
                    OrderNumber = oscl.OrderNumber,
                    CustomerEmail = oscl.CustomerEmail,
                    CustomerName = oscl.CustomerName,
                    OldStatus = oscl.OldStatus,
                    NewStatus = oscl.NewStatus,
                    NewStatusDisplayName = oscl.NewStatusDisplayName,
                    StatusChangedAt = oscl.StatusChangedAt,
                    NotificationSent = oscl.NotificationSent,
                    NotificationChannels = oscl.NotificationChannels
                })
                .ToListAsync();

            return Json(new
            {
                success = true,
                data = logs,
                totalCount = totalCount,
                currentPage = page,
                pageSize = pageSize,
                totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting order status logs");
            return Json(new { success = false, message = "Sipariş durumu geçmişi alınırken hata oluştu." });
        }
    }



    private Guid? GetCompanyId()
    {
        var companyIdClaim = User.FindFirst("CompanyId")?.Value;
        return Guid.TryParse(companyIdClaim, out var companyId) ? companyId : null;
    }

    private async Task<ActiveChannelsViewModel> GetActiveChannels(Guid companyId)
    {
        var hasEmail = await _emailTemplateService.HasSmtpConfigurationAsync(companyId);
        var hasWhatsApp = await _whatsAppTemplateService.HasWhatsAppConfigurationAsync(companyId);

        // SMS için şimdilik false, ileride SMS entegrasyonu eklendiğinde güncellenecek
        var hasSms = false;

        return new ActiveChannelsViewModel
        {
            HasEmail = hasEmail,
            HasSms = hasSms,
            HasWhatsApp = hasWhatsApp
        };
    }

    private async Task<List<OrderStatusNotificationViewModel>> GetOrderStatusNotifications(Guid companyId)
    {
        var notifications = await _context.OrderStatusNotifications
            .Where(osn => osn.CompanyId == companyId)
            .ToListAsync();

        var result = new List<OrderStatusNotificationViewModel>();

        // Tüm sipariş durumları için ayarları oluştur
        foreach (var status in GetOrderStatuses())
        {
            var notification = notifications.FirstOrDefault(n => n.OrderStatus == status.Value);
            result.Add(new OrderStatusNotificationViewModel
            {
                OrderStatus = status.Value,
                OrderStatusDisplayName = status.Text,
                IsActive = notification?.IsActive ?? false,
                EmailNotificationEnabled = notification?.EmailNotificationEnabled ?? false,
                EmailTemplateId = notification?.EmailTemplateId,
                SmsNotificationEnabled = notification?.SmsNotificationEnabled ?? false,
                SmsTemplateId = notification?.SmsTemplateId,
                WhatsAppNotificationEnabled = notification?.WhatsAppNotificationEnabled ?? false,
                WhatsAppTemplateId = notification?.WhatsAppTemplateId,
                DelayMinutes = notification?.DelayMinutes ?? 0,
                TotalNotificationsSent = notification?.TotalNotificationsSent ?? 0,
                LastNotificationAt = notification?.LastNotificationAt
            });
        }

        return result;
    }

    private async Task<List<TemplateSelectItem>> GetEmailTemplates(Guid companyId)
    {
        return (await _emailTemplateService.GetAvailableTemplatesAsync(companyId))
            .Select(cet => new TemplateSelectItem
            {
                Id = cet.Id,
                Name = cet.Name,
                Category = cet.Category
            })
            .ToList();
    }

    private async Task<List<TemplateSelectItem>> GetSmsTemplates(Guid companyId)
    {
        // SMS şablonları için placeholder - ileride SMS template sistemi eklendiğinde güncellenecek
        return new List<TemplateSelectItem>();
    }

    private async Task<List<TemplateSelectItem>> GetWhatsAppTemplates(Guid companyId)
    {
        return (await _whatsAppTemplateService.GetFacebookTemplatesAsync(companyId))
            .Where(x=> x.Status == "APPROVED")
            .Select(t => new TemplateSelectItem
            {
                WhatsappId = t.Id,
                Name = t.Name,
                Category = t.Category
            })
            .ToList();
    }

    private List<OrderStatusSelectItem> GetOrderStatuses()
    {
        return new List<OrderStatusSelectItem>
        {
            new() { Value = "1", Text = "Beklemede" },
            new() { Value = "2", Text = "Onaylandı" },
            new() { Value = "3", Text = "Hazırlanıyor" },
            new() { Value = "4", Text = "Kargoya Verildi" },
            new() { Value = "5", Text = "Teslim Edildi" },
            new() { Value = "6", Text = "İptal Edildi" },
            new() { Value = "7", Text = "İade Edildi" }
        };
    }

    private string GetOrderStatusDisplayName(string status)
    {
        var statusItem = GetOrderStatuses().FirstOrDefault(s => s.Value == status);
        return statusItem?.Text ?? status;
    }
}
