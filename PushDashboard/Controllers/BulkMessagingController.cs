using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.Services.BulkMessaging;
using PushDashboard.ViewModels;

namespace PushDashboard.Controllers;

[Authorize]
public class BulkMessagingController : BaseController
{
    private readonly IBulkMessagingService _bulkMessagingService;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ApplicationDbContext _context;
    private readonly ILogger<BulkMessagingController> _logger;

    public BulkMessagingController(
        IBulkMessagingService bulkMessagingService,
        UserManager<ApplicationUser> userManager,
        ApplicationDbContext context,
        ILogger<BulkMessagingController> logger,
        IUserContextService userContextService)
        : base(userContextService)
    {
        _bulkMessagingService = bulkMessagingService;
        _userManager = userManager;
        _context = context;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> CheckModuleAccess()
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var hasModule = await _bulkMessagingService.HasBulkMessagingModuleAsync(companyId.Value);
            if (!hasModule)
            {
                return Json(new { success = false, message = "Toplu mesaj gönderimi modülüne sahip değilsiniz." });
            }

            var enabledChannels = await _bulkMessagingService.GetEnabledChannelsAsync(companyId.Value);
            if (!enabledChannels.Any())
            {
                return Json(new {
                    success = false,
                    message = "Henüz hiçbir iletişim kanalı aktif edilmemiş. Lütfen modül ayarlarından kanalları aktif edin.",
                    needsConfiguration = true
                });
            }

            return Json(new { success = true, enabledChannels });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking bulk messaging module access for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Modül kontrolü yapılırken hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetAvailableChannels()
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var channels = await _bulkMessagingService.GetAvailableChannelsAsync(companyId.Value);
            return Json(new { success = true, data = channels });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available channels for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Kanallar yüklenirken hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetChannelTemplates(string channelType)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var templates = await _bulkMessagingService.GetChannelTemplatesAsync(companyId.Value, channelType);
            return Json(new { success = true, data = templates });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting templates for channel {ChannelType} and company {CompanyId}", channelType, companyId);
            return Json(new { success = false, message = "Şablonlar yüklenirken hata oluştu." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> EstimateRecipients([FromBody] CustomerFilterViewModel filters)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var count = await _bulkMessagingService.EstimateRecipientsAsync(companyId.Value, filters);
            return Json(new { success = true, count });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error estimating recipients for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Hedef müşteri sayısı hesaplanırken hata oluştu." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> CreatePreview([FromBody] CreateBulkMessageViewModel model)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            var preview = await _bulkMessagingService.CreatePreviewAsync(companyId.Value, model);
            return Json(new { success = true, data = preview });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating preview for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Önizleme oluşturulurken hata oluştu." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> CreateBulkMessage([FromBody] CreateBulkMessageViewModel model)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Json(new { success = false, message = "Kullanıcı bilgisi bulunamadı." });
        }

        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            var (success, message, bulkMessageId) = await _bulkMessagingService.CreateBulkMessageAsync(companyId.Value, user.Id, model);

            if (success && bulkMessageId.HasValue)
            {
                return Json(new { success = true, message, bulkMessageId = bulkMessageId.Value });
            }
            else
            {
                return Json(new { success = false, message });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating bulk message for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Toplu mesaj oluşturulurken hata oluştu." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> StartBulkMessage(int bulkMessageId)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Json(new { success = false, message = "Kullanıcı bilgisi bulunamadı." });
        }

        try
        {
            var (success, message) = await _bulkMessagingService.StartBulkMessageAsync(bulkMessageId, user.Id);
            return Json(new { success, message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting bulk message {BulkMessageId}", bulkMessageId);
            return Json(new { success = false, message = "Toplu mesaj başlatılırken hata oluştu." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> CancelBulkMessage(int bulkMessageId)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Json(new { success = false, message = "Kullanıcı bilgisi bulunamadı." });
        }

        try
        {
            var (success, message) = await _bulkMessagingService.CancelBulkMessageAsync(bulkMessageId, user.Id);
            return Json(new { success, message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling bulk message {BulkMessageId}", bulkMessageId);
            return Json(new { success = false, message = "Toplu mesaj iptal edilirken hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetProgress(int bulkMessageId)
    {
        try
        {
            var progress = await _bulkMessagingService.GetProgressAsync(bulkMessageId);
            if (progress == null)
            {
                return Json(new { success = false, message = "Toplu mesaj bulunamadı." });
            }

            return Json(new { success = true, data = progress });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting progress for bulk message {BulkMessageId}", bulkMessageId);
            return Json(new { success = false, message = "İlerleme bilgisi alınırken hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetBulkMessageDetails(int bulkMessageId)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var details = await _bulkMessagingService.GetBulkMessageDetailsAsync(bulkMessageId, companyId.Value);
            if (details == null)
            {
                return Json(new { success = false, message = "Toplu mesaj bulunamadı." });
            }

            return Json(new { success = true, data = details });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting details for bulk message {BulkMessageId}", bulkMessageId);
            return Json(new { success = false, message = "Detaylar yüklenirken hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetHistory(int page = 1, int pageSize = 20)
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var history = await _bulkMessagingService.GetBulkMessageHistoryAsync(companyId.Value, page, pageSize);
            return Json(new { success = true, data = history });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting bulk message history for company {CompanyId}", companyId);
            return Json(new { success = false, message = "Geçmiş yüklenirken hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetStats()
    {
        var companyId = GetCurrentUserCompanyId();
        if (companyId == null)
        {
            return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
        }

        try
        {
            var stats = await _bulkMessagingService.GetStatsAsync(companyId.Value);
            return Json(new { success = true, data = stats });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting stats for company {CompanyId}", companyId);
            return Json(new { success = false, message = "İstatistikler yüklenirken hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetJobHistory()
    {
        try
        {
            var companyId = GetCurrentCompanyId();

            var jobs = await _context.BulkMessages
                .Where(bm => bm.CompanyId == companyId)
                .OrderByDescending(bm => bm.CreatedAt)
                .Take(20) // Son 20 job
                .Select(bm => new
                {
                    bm.Id,
                    bm.Title,
                    bm.Description,
                    bm.Status,
                    bm.TotalRecipients,
                    bm.ProcessedRecipients,
                    bm.SuccessfulSends,
                    bm.FailedSends,
                    bm.CurrentBatch,
                    bm.TotalBatches,
                    bm.EstimatedCost,
                    bm.ActualCost,
                    bm.CreatedAt,
                    bm.CompletedAt,
                    bm.ErrorMessage
                })
                .ToListAsync();

            return Json(new { success = true, data = jobs });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting job history for company {CompanyId}", GetCurrentCompanyId());
            return Json(new { success = false, message = "Gönderim geçmişi alınırken hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetJobDetails(int id)
    {
        try
        {
            var companyId = GetCurrentCompanyId();

            var job = await _context.BulkMessages
                .Where(bm => bm.Id == id && bm.CompanyId == companyId)
                .Select(bm => new
                {
                    bm.Id,
                    bm.Title,
                    bm.Description,
                    bm.Status,
                    bm.TotalRecipients,
                    bm.ProcessedRecipients,
                    bm.SuccessfulSends,
                    bm.FailedSends,
                    bm.CurrentBatch,
                    bm.TotalBatches,
                    bm.EstimatedCost,
                    bm.ActualCost,
                    bm.CreatedAt,
                    bm.CompletedAt,
                    bm.ErrorMessage
                })
                .FirstOrDefaultAsync();

            if (job == null)
            {
                return Json(new { success = false, message = "Job bulunamadı." });
            }

            return Json(new { success = true, data = job });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting job details for job {JobId}", id);
            return Json(new { success = false, message = "Job detayları alınırken hata oluştu." });
        }
    }

    private Guid GetCurrentCompanyId()
    {
        var companyId = GetCurrentUserCompanyId();
        return companyId ?? Guid.Empty; // Fallback to empty guid if not found
    }
}
